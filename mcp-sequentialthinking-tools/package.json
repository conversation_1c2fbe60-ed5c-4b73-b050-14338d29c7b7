{"name": "mcp-sequentialthinking-tools", "version": "0.0.2", "description": "MCP server for Sequential Thinking Tools", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"mcp-sequentialthinking-tools": "./dist/index.js"}, "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "tsc && chmod +x dist/index.js", "start": "node dist/index.js", "dev": "npx @modelcontextprotocol/inspector dist/index.js", "changeset": "changeset", "version": "changeset version", "release": "pnpm run build && changeset publish"}, "keywords": ["mcp", "model-context-protocol", "sequential-thinking", "problem-solving", "tool-recommendation", "decision-making", "thought-process", "step-by-step", "llm", "ai", "branching-thoughts", "thought-revision", "tool-analysis", "problem-breakdown", "solution-planning", "adaptive-thinking", "reflective-analysis", "tool-confidence"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/spences10/mcp-sequentialthinking-tools.git"}, "bugs": {"url": "https://github.com/spences10/mcp-sequentialthinking-tools/issues"}, "homepage": "https://github.com/spences10/mcp-sequentialthinking-tools#readme", "dependencies": {"@modelcontextprotocol/sdk": "1.11.5", "chalk": "^5.4.1"}, "devDependencies": {"@changesets/cli": "^2.29.4", "@types/node": "^22.15.21", "typescript": "^5.8.3"}}