# Research Organization Implementation Guide

This guide provides instructions for implementing the research organization plan for the Nixtla-Forecasting-Ensemble project. The reorganization scripts and templates create a structured, maintainable repository for research documents and code examples.

## Files Included

- `Research-Organization-Plan.md` - Comprehensive plan document
- `create_research_structure.py` - <PERSON><PERSON><PERSON> to create the folder structure
- `migrate_research_files.py` - <PERSON><PERSON><PERSON> to migrate existing files
- `templates/` - Document templates for new content
  - `ResearchReportTemplate.md`
  - `ImplementationGuideTemplate.md`
  - `PythonScriptTemplate.py`

## Implementation Steps

### 1. Create Directory Structure

Run the following command to create the new research directory structure:

```bash
python create_research_structure.py
```

This creates a `Research-New` directory with the complete folder hierarchy, including index files for each directory.

### 2. Migrate Existing Files

Run the following command to migrate existing files to the new structure:

```bash
python migrate_research_files.py
```

This script:
- Migrates files according to the predefined mapping in `FILE_MAPPING`
- Automatically detects and migrates unmapped files
- Adds appropriate metadata to each file
- Updates index files with directory contents

### 3. Review and Finalize

After running the scripts:

1. Review the new structure in `Research-New`
2. Verify file organization and metadata
3. Make any necessary manual adjustments
4. When satisfied, replace the original `Research` directory:

```bash
# Backup original Research directory (recommended)
mv Research Research-Original

# Move new structure to replace the original
mv Research-New Research
```

## Adding New Content

### Using Templates

1. Copy the appropriate template from the `templates/` directory
2. Fill in the metadata fields in the frontmatter/header
3. Add your content following the template structure
4. Save the file in the appropriate directory with the proper naming convention

### Naming Convention

- Use the format: `[nn]_[descriptive-name].[extension]`
- Start with a two-digit number for ordering
- Use kebab-case for the descriptive name
- Examples:
  - `01_bitcoin-volatility-forecasting.md`
  - `02_lstm-prediction-model.py`

### Required Metadata

For Markdown files:
- title
- date
- author
- category
- tags
- summary

For Python files:
- Title
- Date
- Author
- Category
- Tags
- Summary
- Dependencies
- Usage

## Directory Structure Reference

```
Research/
├── 01-Foundations/
│   ├── Concepts/
│   ├── Models/
│   └── Evaluation/
├── 02-Domain-Applications/
│   ├── Cryptocurrency/
│   └── Financial-Markets/
├── 03-Techniques/
│   ├── Decomposition/
│   ├── Feature-Engineering/
│   ├── Ensembling/
│   ├── Uncertainty-Quantification/
│   └── Optimization/
├── 04-Implementation/
│   ├── Libraries/
│   ├── Code-Examples/
│   └── Integration/
├── 05-Case-Studies/
│   ├── Benchmarks/
│   ├── Performance-Analysis/
│   └── Real-World-Applications/
└── 06-Resources/
    ├── Datasets/
    ├── Literature-Reviews/
    └── References/
```

## Maintenance

- Run `update_index_files()` from the migration script when adding multiple new files to update indexes
- Follow the same metadata format and naming conventions for all new content
- Periodically review the organization to ensure it remains effective

## Troubleshooting

- If file migration fails, check file encoding issues
- If metadata extraction fails, manually add appropriate frontmatter
- For any permissions issues, ensure write permissions on the directory structure

For detailed information about the organization plan, refer to `Research-Organization-Plan.md`.