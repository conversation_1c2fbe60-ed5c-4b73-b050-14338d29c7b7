```markdown
## Bug Fix Plan: [Bug Description]

### Root Cause Analysis

- Symptoms: [Observed behavior]
- Suspected causes: [Possible causes]
- Verification steps: [How to verify cause]

### Fix Implementation

1. [Step 1]
   - Expected outcome: [Outcome]
   - Verification: [How to verify]
2. [Step 2]
   - Expected outcome: [Outcome]
   - Verification: [How to verify]
...

### Testing Strategy

- Reproduction test: [How to confirm bug]
- Fix verification: [How to verify fix]
- Regression tests: [What else might be affected]

### Potential Side Effects

- [Side effect 1]: [Mitigation strategy]
- [Side effect 2]: [Mitigation strategy]
- ...
```
