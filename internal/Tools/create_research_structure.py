#!/usr/bin/env python3
"""
Title: Create Research Directory Structure
Date: 2025-05-20
Author: Claude
Category: Implementation/Organization
Tags: directory structure, organization, research
Summary: <PERSON>rip<PERSON> to create the hierarchical folder structure for the Nixtla-Forecasting-Ensemble research repository

Dependencies:
- os
- pathlib

Usage:
python create_research_structure.py
"""

from pathlib import Path

def create_directory_structure(base_path):
    """
    Create the hierarchical directory structure for the research repository

    Args:
        base_path: Base path where the structure will be created
    """
    # Define the directory structure
    directory_structure = {
        "01-Foundations": {
            "Concepts": {
                "Time-Series-Basics": {},
                "Statistical-Methods": {}
            },
            "Models": {
                "Statistical": {},
                "Machine-Learning": {},
                "Deep-Learning": {}
            },
            "Evaluation": {
                "Metrics": {},
                "Validation-Methods": {}
            }
        },
        "02-Domain-Applications": {
            "Cryptocurrency": {
                "Bitcoin": {},
                "General": {}
            },
            "Financial-Markets": {}
        },
        "03-Techniques": {
            "Decomposition": {},
            "Feature-Engineering": {},
            "Ensembling": {},
            "Uncertainty-Quantification": {},
            "Optimization": {}
        },
        "04-Implementation": {
            "Libraries": {
                "Nixtla": {
                    "NeuralForecast": {},
                    "StatsForecast": {},
                    "MLForecast": {}
                },
                "GluonTS": {},
                "Other": {}
            },
            "Code-Examples": {
                "Python": {},
                "Notebooks": {}
            },
            "Integration": {
                "Data-Pipelines": {},
                "System-Architecture": {}
            }
        },
        "05-Case-Studies": {
            "Benchmarks": {},
            "Performance-Analysis": {},
            "Real-World-Applications": {}
        },
        "06-Resources": {
            "Datasets": {},
            "Literature-Reviews": {},
            "References": {}
        }
    }

    # Create base research directory
    base_dir = Path(base_path) / "Research-New"
    base_dir.mkdir(exist_ok=True)

    # Create directory index template
    index_template = """---
title: "{title}"
description: "{description}"
---

# {title}

{description}

## Contents

This directory contains the following:

{content_list}

## Related Directories

{related_dirs}
"""

    # Create directories recursively
    def create_dirs(parent_path, structure, depth=0):
        for name, children in structure.items():
            # Create directory
            current_path = parent_path / name
            current_path.mkdir(exist_ok=True)
            print(f"Created directory: {current_path}")

            # Create index file
            title = name.replace("-", " ")
            description = f"This directory contains research materials related to {title.lower()}"

            # Placeholder for content list and related directories
            content_list = "- (No content yet)"
            related_dirs = "- (No related directories yet)"

            # Write index file
            index_content = index_template.format(
                title=title,
                description=description,
                content_list=content_list,
                related_dirs=related_dirs
            )

            index_path = current_path / "_index.md"
            with open(index_path, "w") as f:
                f.write(index_content)

            # Create subdirectories
            create_dirs(current_path, children, depth + 1)

    # Start creating directories
    create_dirs(base_dir, directory_structure)
    print(f"\nDirectory structure created successfully at {base_dir}")
    print("The new structure is created in 'Research-New' to avoid overwriting existing files.")
    print("Please migrate content from 'Research' to 'Research-New' and then rename directories when ready.")

if __name__ == "__main__":
    # Get the project path (go up two levels from internal/Tools to project root)
    current_dir = Path(__file__).parent.parent.parent

    # Create the directory structure
    create_directory_structure(current_dir)