#!/usr/bin/env python3
"""
<PERSON>ript to convert Jupyter notebooks (.ipynb) to Python files (.py)
"""

import os
import subprocess
import glob

def convert_notebook_to_python(notebook_path):
    """
    Convert a Jupyter notebook to a Python file using nbconvert
    
    Args:
        notebook_path (str): Path to the notebook file
    
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        # Get the base name without extension
        base_name = os.path.splitext(notebook_path)[0]
        
        # Run nbconvert to convert the notebook to Python
        cmd = ["jupyter", "nbconvert", "--to", "python", notebook_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"Successfully converted {notebook_path} to {base_name}.py")
            return True
        else:
            print(f"Failed to convert {notebook_path}. Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"Error converting {notebook_path}: {str(e)}")
        return False

def main():
    # Path to the directory containing notebooks
    directory = "Research/Methods:Techniques/"
    
    # Find all .ipynb files in the directory
    notebook_files = glob.glob(os.path.join(directory, "*.ipynb"))
    
    if not notebook_files:
        print(f"No .ipynb files found in {directory}")
        return
    
    print(f"Found {len(notebook_files)} notebook files to convert")
    
    # Convert each notebook to Python
    successful = 0
    for notebook in notebook_files:
        if convert_notebook_to_python(notebook):
            successful += 1
    
    print(f"Conversion complete. Successfully converted {successful} out of {len(notebook_files)} notebooks.")

if __name__ == "__main__":
    main()
