#!/usr/bin/env python3
"""
Title: Research File Migration Script
Date: 2025-05-20
Author: Claude
Category: Implementation/Organization
Tags: migration, organization, research, metadata
Summary: Script to migrate existing research files to the new directory structure with proper naming and metadata

Dependencies:
- os
- pathlib
- shutil
- re
- yaml
- datetime

Usage:
python migrate_research_files.py
"""

import re
from datetime import datetime
from pathlib import Path

# Mapping dictionary for file migrations
# Format: 'original_path': ('new_directory', 'new_filename', 'category', ['tag1', 'tag2'])
FILE_MAPPING = {
    # Current Bitcoin application files
    'Research/Applications/Bitcoin/intraday_forecasting-guide.md':
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '01_intraday-forecasting-guide.md',
         'Domain-Applications/Cryptocurrency/Bitcoin', ['bitcoin', 'forecasting', 'intraday', 'guide']),

    # Current Forecasting application files
    'Research/Forecasting/Applications/bitcoin_forecasting-guide.md':
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '02_bitcoin-forecasting-guide.md',
         'Domain-Applications/Cryptocurrency/Bitcoin', ['bitcoin', 'forecasting', 'guide']),

    'Research/Forecasting/Applications/bitcoin_forecasting-advanced.md':
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '03_bitcoin-forecasting-advanced.md',
         'Domain-Applications/Cryptocurrency/Bitcoin', ['bitcoin', 'forecasting', 'advanced']),

    # Cryptocurrency subdirectory files
    'Research/Forecasting/Applications/Cryptocurrency/neuralforecast_bitcoin-implementation-guide.md':
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '04_neuralforecast-bitcoin-implementation-guide.md',
         'Domain-Applications/Cryptocurrency/Bitcoin', ['neuralforecast', 'bitcoin', 'implementation', 'guide']),

    'Research/Forecasting/Applications/Cryptocurrency/advanced_crypto-forecasting-techniques.md':
        ('02-Domain-Applications/Cryptocurrency/General', '01_advanced-crypto-forecasting-techniques.md',
         'Domain-Applications/Cryptocurrency/General', ['cryptocurrency', 'forecasting', 'techniques', 'advanced']),

    'Research/Forecasting/Applications/Cryptocurrency/advanced-techniques_short-term-forecasting.md':
        ('02-Domain-Applications/Cryptocurrency/General', '02_advanced-techniques-short-term-forecasting.md',
         'Domain-Applications/Cryptocurrency/General', ['cryptocurrency', 'short-term', 'forecasting', 'techniques']),

    'Research/Forecasting/Applications/Cryptocurrency/statistical_crypto-forecasting-techniques.md':
        ('02-Domain-Applications/Cryptocurrency/General', '03_statistical-crypto-forecasting-techniques.md',
         'Domain-Applications/Cryptocurrency/General', ['cryptocurrency', 'statistical', 'forecasting', 'techniques']),

    # Architecture files
    'Research/Architecture/modular_hybrid-forecasting-architecture.md':
        ('04-Implementation/Integration/System-Architecture', '01_modular-hybrid-forecasting-architecture.md',
         'Implementation/Integration/System-Architecture', ['architecture', 'modular', 'hybrid', 'forecasting']),

    # Techniques files
    'Research/Forecasting/Techniques/ensemble_dynamic-weighting-strategies.md':
        ('03-Techniques/Ensembling', '01_ensemble-dynamic-weighting-strategies.md',
         'Techniques/Ensembling', ['ensemble', 'dynamic-weighting', 'strategies']),

    'Research/Forecasting/Techniques/decomposition_time-series-example.py':
        ('03-Techniques/Decomposition', '01_decomposition-time-series-example.py',
         'Techniques/Decomposition', ['decomposition', 'time-series', 'example', 'python']),

    'Research/Forecasting/Techniques/moving-average_time-series-example.py':
        ('03-Techniques/Decomposition', '02_moving-average-time-series-example.py',
         'Techniques/Decomposition', ['moving-average', 'time-series', 'example', 'python']),

    # Model files
    'Research/Forecasting/Models/lstm_sine-wave-example.py':
        ('01-Foundations/Models/Deep-Learning', '01_lstm-sine-wave-example.py',
         'Foundations/Models/Deep-Learning', ['lstm', 'sine-wave', 'example', 'deep-learning']),

    'Research/Models/DecisionForests/tensorflow_classification-example.py':
        ('01-Foundations/Models/Machine-Learning', '01_tensorflow-classification-example.py',
         'Foundations/Models/Machine-Learning', ['tensorflow', 'classification', 'decision-forests', 'machine-learning']),

    'Research/Models/Neural/simple_neural-network-regression.py':
        ('01-Foundations/Models/Deep-Learning', '02_simple-neural-network-regression.py',
         'Foundations/Models/Deep-Learning', ['neural-network', 'regression', 'simple', 'deep-learning']),

    # Synthesis files
    'Research/Synthesis/bitcoin_forecasting-complete-synthesis.md':
        ('05-Case-Studies/Real-World-Applications', '01_bitcoin-forecasting-complete-synthesis.md',
         'Case-Studies/Real-World-Applications', ['bitcoin', 'forecasting', 'synthesis', 'complete']),

    'Research/Synthesis/bitcoin_forecasting-technical-research.md':
        ('06-Resources/Literature-Reviews', '01_bitcoin-forecasting-technical-research.md',
         'Resources/Literature-Reviews', ['bitcoin', 'forecasting', 'technical-research', 'literature']),
}

def extract_title_from_content(file_path):
    """Extract title from the first line of a file if it starts with # or similar"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            if first_line.startswith('#'):
                return first_line.lstrip('#').strip()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")

    # If no title found, use the filename as title
    return Path(file_path).stem.replace('_', ' ').replace('-', ' ').title()

def create_markdown_frontmatter(file_path, category, tags):
    """Create YAML frontmatter for markdown files"""
    title = extract_title_from_content(file_path)
    today = datetime.now().strftime('%Y-%m-%d')

    # Create YAML frontmatter manually to avoid yaml dependency
    frontmatter = f"""title: "{title}"
date: "{today}"
last_updated: "{today}"
author: "Nixtla Team"
category: "{category}"
tags: {tags}
difficulty: "intermediate"
models: []
libraries: ["nixtla"]
related_documents: []
status: "migrated"
summary: "Migrated document about {title}"
"""

    return frontmatter

def create_python_header(file_path, category, tags):
    """Create header comment block for Python files"""
    title = Path(file_path).stem.replace('_', ' ').replace('-', ' ').title()
    today = datetime.now().strftime('%Y-%m-%d')

    header = f'''"""
Title: {title}
Date: {today}
Last Updated: {today}
Author: Nixtla Team
Category: {category}
Tags: {', '.join(tags)}
Difficulty: intermediate
Models:
Libraries: nixtla
Related Documents:
Status: migrated
Summary: Migrated script for {title}

Dependencies:
- numpy
- pandas
- matplotlib

Usage:
python {Path(file_path).name}
"""
'''
    return header

def migrate_file(project_path, orig_path, dest_info):
    """Migrate a single file to its new location with proper metadata"""
    dest_dir, dest_filename, category, tags = dest_info

    # Full paths
    source_path = project_path / orig_path
    target_dir = project_path / 'Research-New' / dest_dir
    target_path = target_dir / dest_filename

    # Ensure target directory exists
    target_dir.mkdir(parents=True, exist_ok=True)

    # If source doesn't exist, skip
    if not source_path.exists():
        print(f"Source file not found: {source_path}")
        return

    print(f"Migrating: {source_path} -> {target_path}")

    # Read content
    try:
        with open(source_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {source_path}: {e}")
        return

    # Create appropriate metadata based on file type
    if source_path.suffix.lower() == '.md':
        # Create YAML frontmatter
        frontmatter = create_markdown_frontmatter(source_path, category, tags)
        new_content = f"---\n{frontmatter}---\n\n{content}"
    elif source_path.suffix.lower() == '.py':
        # Create Python header comment
        header = create_python_header(source_path, category, tags)

        # Check if file already has a docstring at the top
        if content.lstrip().startswith('"""') or content.lstrip().startswith("'''"):
            # Find the end of the existing docstring
            triple_quote = '"""' if content.lstrip().startswith('"""') else "'''"
            end_idx = content.find(triple_quote, content.find(triple_quote) + 3)
            if end_idx != -1:
                # Replace existing docstring
                new_content = content[:content.find(triple_quote)] + header + content[end_idx + 3:]
            else:
                new_content = header + content
        else:
            new_content = header + content
    else:
        # For other file types, just copy as is
        new_content = content

    # Write to new location
    try:
        with open(target_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"Successfully migrated: {target_path}")
    except Exception as e:
        print(f"Error writing {target_path}: {e}")

def migrate_unmapped_files(project_path):
    """Process files not explicitly mapped"""
    research_dir = project_path / 'Research'

    # Find all files in Research directory recursively
    for file_path in research_dir.rglob('*'):
        if file_path.is_file() and file_path.name != 'CLAUDE.md':
            relative_path = str(file_path.relative_to(project_path))

            # Skip if already mapped
            if relative_path in FILE_MAPPING:
                continue

            print(f"Found unmapped file: {relative_path}")

            # Determine destination based on file location and content
            if 'Applications' in str(file_path):
                dest_dir = '02-Domain-Applications/Cryptocurrency/General'
                category = 'Domain-Applications/Cryptocurrency/General'
            elif 'Architecture' in str(file_path):
                dest_dir = '04-Implementation/Integration/System-Architecture'
                category = 'Implementation/Integration/System-Architecture'
            elif 'Techniques' in str(file_path):
                dest_dir = '03-Techniques/Feature-Engineering'
                category = 'Techniques/Feature-Engineering'
            elif 'Models' in str(file_path):
                dest_dir = '01-Foundations/Models/Machine-Learning'
                category = 'Foundations/Models/Machine-Learning'
            else:
                dest_dir = '06-Resources/References'
                category = 'Resources/References'

            # Generate file number
            target_dir = project_path / 'Research-New' / dest_dir
            existing_files = list(target_dir.glob(f'*{file_path.suffix}'))
            file_num = len(existing_files) + 1

            # Create new filename
            new_filename = f"{file_num:02d}_{file_path.stem.lower().replace(' ', '-')}{file_path.suffix}"

            # Define tags
            tags = [file_path.stem.lower().replace(' ', '-').replace('_', '-')]

            # Migrate file
            migrate_file(project_path, relative_path, (dest_dir, new_filename, category, tags))

def update_index_files(project_path):
    """Update _index.md files with information about the directory contents"""
    research_dir = project_path / 'Research-New'

    for index_file in research_dir.glob('**/_index.md'):
        dir_path = index_file.parent

        # Get list of files in this directory (excluding _index.md)
        files = [f for f in dir_path.glob('*') if f.name != '_index.md' and not f.is_dir()]

        # Read current index content
        with open(index_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Update content list
        content_list = "\n".join([f"- [{f.name}](./{f.name})" for f in files])
        if not content_list:
            content_list = "- (No content yet)"

        # Update the content list in the index file
        content = re.sub(r'## Contents\n\nThis directory contains the following:\n\n.*?\n\n',
                         f'## Contents\n\nThis directory contains the following:\n\n{content_list}\n\n',
                         content, flags=re.DOTALL)

        # Write updated content
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"Updated index file: {index_file}")

def main():
    """Main function to run the migration process"""
    # Get project path (go up two levels from internal/Tools to project root)
    project_path = Path(__file__).parent.parent.parent

    print(f"Starting migration process for {project_path}")

    # Process explicitly mapped files
    for orig_path, dest_info in FILE_MAPPING.items():
        migrate_file(project_path, orig_path, dest_info)

    # Process unmapped files
    migrate_unmapped_files(project_path)

    # Update index files
    update_index_files(project_path)

    print("\nMigration completed successfully!")
    print("Files have been migrated to the 'Research-New' directory.")
    print("Review the new structure and files before replacing the original 'Research' directory.")

if __name__ == "__main__":
    main()