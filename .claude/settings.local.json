{"permissions": {"allow": ["mcp__code-reasoning__code-reasoning", "<PERSON><PERSON>(mv:*)", "mcp__basic-memory__project_info", "<PERSON><PERSON>(mkdir:*)", "mcp__basic-memory__write_note", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(git clone:*)", "Bash(claude mcp add vibe-coder:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(mcp:*)", "mcp__vibe-coder__start_feature_clarification", "mcp__vibe-coder__provide_clarification", "mcp__vibe-coder__generate_prd", "mcp__vibe-coder__create_phase", "mcp__vibe-coder__get_document_path", "Bash(ls:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(npx:*)", "Bash(npm info:*)", "mcp__software-planning-tool__start_planning", "Bash(find:*)", "Bash(grep:*)", "mcp__basic-memory__recent_activity", "WebFetch(domain:memory.basicmachines.co)", "Bash(npm search:*)", "Bash(uv tool install:*)", "mcp__basic-memory__search_notes", "mcp__basic-memory__canvas", "mcp__software-planning-tool__get_todos", "mcp__basic-memory__read_note", "mcp__basic-memory__delete_note", "mcp__software-planning-tool__add_todo", "mcp__software-planning-tool__update_todo_status", "mcp__software-planning-tool__save_plan", "Bash(claude --version)", "Bash(brew update:*)", "Bash(brew upgrade:*)", "Bash(npm update:*)", "mcp__basic-memory__build_context", "Bash(cp:*)", "Bash(gh auth:*)", "Bash(gh issue create:*)", "<PERSON><PERSON>(chmod:*)", "mcp__sequential-thinking__sequentialthinking_tools"], "deny": []}}