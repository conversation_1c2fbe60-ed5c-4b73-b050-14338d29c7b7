---
title: "General Cryptocurrency Forecasting Research"
permalink: "domain-applications/cryptocurrency/general/index"
type: "index"
created: "2025-05-24"
last_updated: "2025-05-24"
tags:
  - cryptocurrency
  - forecasting
  - statistical-methods
  - time-series
  - advanced-techniques
  - general
  - index
models:
  - "ARIMA"
  - "ETS"
  - "MSTL"
  - "GARCH"
  - "HMM"
  - "MarkovRegression"
  - "Theta"
libraries:
  - "StatsForecast"
  - "MLForecast"
  - "statsmodels"
  - "ARCH"
  - "Optuna"
  - "scikit-learn"
techniques:
  - "time-series-decomposition"
  - "feature-engineering"
  - "ensemble-methods"
  - "hyperparameter-optimization"
  - "regime-switching"
  - "volatility-modeling"
  - "prediction-intervals"
datasets:
  - "Cryptocurrency hourly"
  - "Technical indicators"
  - "Social sentiment"
  - "On-chain metrics"
complexity: "intermediate-to-expert"
summary: "General cryptocurrency forecasting research covering advanced statistical techniques, feature engineering, ensemble methods, and comprehensive frameworks for improving forecasting accuracy across various cryptocurrency assets"
related:
  - "domain-applications/cryptocurrency/bitcoin/index"
  - "techniques/ensembling"
  - "techniques/feature-engineering"
---

# General Cryptocurrency Forecasting Research

This directory contains comprehensive research materials for general cryptocurrency forecasting techniques, covering advanced statistical methods, feature engineering, and ensemble approaches applicable across various cryptocurrency assets.

## Contents

### Advanced Techniques and Frameworks

- [01_advanced-crypto-forecasting-techniques.md](./01_advanced-crypto-forecasting-techniques.md) - Comprehensive framework for advanced cryptocurrency forecasting combining time series decomposition, feature engineering, ensemble methods, and volatility-aware risk management

- [02_advanced-techniques-short-term-forecasting.md](./02_advanced-techniques-short-term-forecasting.md) - Eight advanced techniques to improve short-term (24-hour) cryptocurrency price forecasts using hybrid statistical and machine learning approaches

- [03_statistical-crypto-forecasting-techniques.md](./03_statistical-crypto-forecasting-techniques.md) - Expert-level research on advanced statistical techniques for cryptocurrency forecasting, focusing on optimal decomposition, parameter optimization, and prediction intervals

## Research Focus Areas

**Statistical Methods:**
- Time series decomposition (STL, MSTL, X-13ARIMA-SEATS)
- Advanced ARIMA/ETS/GARCH modeling
- Regime switching models and volatility clustering

**Feature Engineering:**
- Technical indicator selection and multicollinearity handling
- External data integration (sentiment, on-chain metrics)
- Rolling statistics and volatility measures

**Advanced Techniques:**
- Bayesian optimization for hyperparameter tuning
- Ensemble methods and model combination
- Prediction interval calibration and risk management

## Related Directories

- [../Bitcoin/](../Bitcoin/) - Bitcoin-specific forecasting research and implementations
- [../../../03-Techniques/](../../../03-Techniques/) - General forecasting techniques and methods
- [../../../05-Case-Studies/](../../../05-Case-Studies/) - Real-world applications and benchmarks