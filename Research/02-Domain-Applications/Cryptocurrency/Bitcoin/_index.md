---
title: "Bitcoin Forecasting Research"
description: "Comprehensive research materials for Bitcoin price forecasting using advanced time series methods"
permalink: "domain-applications/cryptocurrency/bitcoin/index"
---

# Bitcoin Forecasting Research

This directory contains comprehensive research materials for Bitcoin price forecasting, covering everything from basic implementation guides to advanced production-ready systems.

## Contents

### Implementation Guides
- [01_intraday-forecasting-guide.md](./01_intraday-forecasting-guide.md) - Intraday Bitcoin forecasting strategies
- [02_bitcoin-forecasting-guide.md](./02_bitcoin-forecasting-guide.md) - General Bitcoin forecasting guide
- [03_bitcoin-forecasting-advanced.md](./03_bitcoin-forecasting-advanced.md) - Advanced Bitcoin forecasting techniques
- [04_neuralforecast-bitcoin-implementation-guide.md](./04_neuralforecast-bitcoin-implementation-guide.md) - Practical NeuralForecast implementation

### Comprehensive Research
- [05_neuralforecast-bitcoin-research-comprehensive.md](./05_neuralforecast-bitcoin-research-comprehensive.md) - **Complete technical research report** on production Bitcoin forecasting with NeuralForecast, covering architecture, GPU optimization, ensemble methods, and MLOps deployment

## Research Progression

**Beginner Path**: Start with `01_intraday-forecasting-guide.md` → `02_bitcoin-forecasting-guide.md`

**Intermediate Path**: `04_neuralforecast-bitcoin-implementation-guide.md` → `03_bitcoin-forecasting-advanced.md`

**Advanced/Production Path**: `05_neuralforecast-bitcoin-research-comprehensive.md` (comprehensive production guide)

## Related Directories

- [../General/](../General/) - General cryptocurrency forecasting techniques
- [../../../01-Foundations/Models/Deep-Learning/](../../../01-Foundations/Models/Deep-Learning/) - Deep learning model foundations
- [../../../05-Case-Studies/Benchmarks/](../../../05-Case-Studies/Benchmarks/) - Model performance benchmarks