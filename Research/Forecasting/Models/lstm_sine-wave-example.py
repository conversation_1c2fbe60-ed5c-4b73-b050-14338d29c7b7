#!/usr/bin/env python
# coding: utf-8

"""
# LSTM RNN time series prediction: univariate sine wave example

## Metadata
title: LSTM Implementation for Sine Wave Prediction
type: example
tags: LSTM, time-series, sine-wave, keras, prediction
related: 
  - forecasting/techniques/decomposition_time-series-example
  - forecasting/models/lstm_bitcoin-example
models: LSTM
techniques: time-series-prediction, recurrent-neural-networks, extrapolation
datasets: synthetic-sine-wave
complexity: beginner

## Summary
This example demonstrates how to implement an LSTM recurrent neural network
for time series prediction using a simple sine wave and extending to sine wave
with harmonics. Shows basic TimeseriesGenerator usage and extrapolation methods.

## Author
<PERSON> (https://www.kaggle.com/carlmcbrideellis)
"""

import numpy as np
import matplotlib.pyplot as plt

import keras
seed = 101
keras.utils.set_random_seed(seed)


X_train = np.arange(0,100,0.5) 
y_train = np.sin(X_train)

X_test = np.arange(100,200,0.5) 
y_test = np.sin(X_test)

n_features = 1

train_series = y_train.reshape((len(y_train), n_features))
test_series  = y_test.reshape((len(y_test), n_features))


fig, ax = plt.subplots(1, 1, figsize=(15, 4))
ax.plot(X_train,y_train, lw=3, label='train data')
ax.plot(X_test, y_test,  lw=3, label='test data')
ax.legend(loc="lower left")
plt.show();


from tensorflow.keras.preprocessing.sequence import TimeseriesGenerator

look_back  = 20

train_generator = TimeseriesGenerator(train_series, train_series,
                                      length        = look_back, 
                                      sampling_rate = 1,
                                      stride        = 1,
                                      batch_size    = 1)

test_generator = TimeseriesGenerator(test_series, test_series,
                                      length        = look_back, 
                                      sampling_rate = 1,
                                      stride        = 1,
                                      batch_size    = 1)


from keras.models import Sequential
from keras import Input
from keras.layers import LSTM
from keras.layers import Dense

n_neurons  = 2

model = Sequential()
model.add(Input(shape=(look_back, n_features)))
model.add(LSTM(n_neurons))
model.add(Dense(1))
model.compile(optimizer='adam', loss='mse')

initial_weights = model.get_weights()
model.fit(train_generator, epochs=300, verbose=None)

# ### Prediction


test_predictions  = model.predict(test_generator)


x = np.arange(110,200,0.5)
fig, ax = plt.subplots(1, 1, figsize=(15, 5))
ax.plot(X_train,y_train, lw=2, label='train data')
ax.plot(X_test,y_test, lw=3, c='y', label='test data')
ax.plot(x,test_predictions, lw=3, c='r',linestyle = ':', label='predictions')
ax.legend(loc="lower left")
plt.show();

# ### Extrapolation


extrapolation = list()
seed_batch    = y_test[:look_back].reshape((1,look_back, n_features))
current_batch = seed_batch

# extrapolate the next 180 values
for i in range(180):
    predicted_value = model.predict(current_batch, verbose=None)[0]
    extrapolation.append(predicted_value) 
    current_batch = np.append(current_batch[:,1:,:],[[predicted_value]],axis=1)


x = np.arange(110,200,0.5)
fig, ax = plt.subplots(1, 1, figsize=(15, 5))
ax.plot(X_train,y_train, lw=2, label='train data')
ax.plot(X_test,y_test, lw=3, c='y', label='test data')
ax.plot(x,extrapolation, lw=3, c='r',linestyle = ':', label='extrapolation')
ax.legend(loc="lower left")
plt.show();

# ## Sine wave + 3rd harmonic + 5th harmonic
# Let us complicate things a little by now adding the third and fifth harmonics to the input data


X_train = np.arange(0,50,0.5) 
y_train = np.sin(X_train) + np.sin(3*X_train)/3 + np.sin(5*X_train)/5

X_test  = np.arange(50,100,0.5) 
y_test  = np.sin(X_train) + np.sin(3*X_train)/3 + np.sin(5*X_train)/5


train_series = y_train.reshape((len(y_train), n_features))
test_series  = y_test.reshape((len(y_test), n_features))
fig, ax = plt.subplots(1, 1, figsize=(15, 4))
ax.plot(X_train,y_train, lw=2, label='train data')
ax.plot(X_test, y_test,  lw=2, label='test data')
ax.legend(loc="lower left")
plt.show();


train_generator = TimeseriesGenerator(train_series, train_series,
                                      length        = look_back, 
                                      sampling_rate = 1,
                                      stride        = 1,
                                      batch_size    = 1)

test_generator = TimeseriesGenerator(test_series, test_series,
                                      length        = look_back, 
                                      sampling_rate = 1,
                                      stride        = 1,
                                      batch_size    = 1)

model.set_weights(initial_weights) # re-set the weights
model.fit(train_generator, epochs=1000, verbose=None)

# ### Prediction


test_predictions  = model.predict(test_generator)
x = np.arange(60,100,0.5)
fig, ax = plt.subplots(1, 1, figsize=(15, 5))
ax.plot(X_train,y_train, lw=2, label='train data')
ax.plot(X_test,y_test, lw=3, c='y', label='test data')
ax.plot(x,test_predictions, lw=3, c='r',linestyle = ':', label='predictions')
ax.legend(loc="lower left")
plt.show();

# ### Extrapolation


extrapolation = list()
seed_batch    = y_test[:look_back].reshape((1,look_back, n_features))
current_batch = seed_batch

# extrapolate the next 180 values
for i in range(400):
    predicted_value = model.predict(current_batch, verbose=None)[0]
    extrapolation.append(predicted_value) 
    current_batch = np.append(current_batch[:,1:,:],[[predicted_value]],axis=1)

# plot
x = np.arange(60,260,0.5)
fig, ax = plt.subplots(1, 1, figsize=(15, 5))
ax.plot(X_train,y_train, lw=2, label='train data')
ax.plot(X_test,y_test, lw=3, c='y', label='test data')
ax.plot(x,extrapolation, lw=1, c='r', label='extrapolation')
ax.legend(loc="lower left")
plt.show();

# ## Related reading
# * [Sepp Hochreiter and Jürgen Schmidhuber "Long Short-Term Memory", Neural Computation **9** pp. 1735-1780 (1997)](https://www.mitpressjournals.org/doi/pdf/10.1162/neco.1997.9.8.1735)
# * [Long short-term memory](https://en.wikipedia.org/wiki/Long_short-term_memory) Wikipedia
# * [keras LSTM layers](https://keras.io/api/layers/recurrent_layers/lstm/)
# * [keras TimeseriesGenerator](https://www.tensorflow.org/api_docs/python/tf/keras/preprocessing/sequence/TimeseriesGenerator)