#!/usr/bin/env python
# coding: utf-8

"""
# Time series decomposition example

## Metadata
title: Basic Time Series Decomposition Using Statsmodels
type: example
tags: time-series, decomposition, statsmodels, seasonality, trend
related: 
  - forecasting/models/lstm_sine-wave-example
  - forecasting/techniques/ensemble_dynamic-weighting-strategies
models: seasonal_decompose
techniques: time-series-decomposition, additive-decomposition
datasets: carbon-dioxide-levels
complexity: beginner

## Summary
This example demonstrates basic time series decomposition into trend, seasonal, 
and residual components using the statsmodels library. It uses atmospheric carbon 
dioxide level data to illustrate the approach with a clearly seasonal pattern.
"""

# # Time series decomposition example
# In this short notebook we shall be decomposing a time series into three components; the overall trend, the seasonal component, and finally a noise, or residual, term:
# 
# Y[t] = Trend[t] + Seasonal[t] + residual[t]
# 
# To do this we shall me making use of [statsmodels.tsa.seasonal.seasonal_decompose](https://www.statsmodels.org/stable/generated/statsmodels.tsa.seasonal.seasonal_decompose.html). For data we shall be using the [Carbon Dioxide Levels in Atmosphere](https://www.kaggle.com/ucsandiego/carbon-dioxide) published by the University of California's Scripps Institution of Oceanography. We shall assume that there is a periodicity of 12 months.

import numpy as np
import matplotlib.pyplot as plt
plt.rcParams.update({'font.size': 18})
plt.style.use('fivethirtyeight')
import pandas as pd


# read in the data
data = pd.read_csv("../input/carbon-dioxide/archive.csv", parse_dates= {"date" : ["Year","Month"]})
# remove any NaN data
data = data.dropna(how='any', subset=['Carbon Dioxide (ppm)'])
# select the data between 1970 and 2017
data = data[data['date'].between('1970-01-01', '2016-12-01')]
data = data.set_index('date')
# take a look 
data

# Plot the data we shall be decomposing


data.plot(y='Carbon Dioxide (ppm)', kind='line',figsize=(12,5), lw=2, title="Carbon Dioxide Levels in Atmosphere");

# Perform the time series decomposition


from statsmodels.tsa.seasonal import seasonal_decompose
additiveDecomposition = seasonal_decompose(data['Carbon Dioxide (ppm)'], model='additive', period=12)

# Now let us superimpose the overall trend on top of the original data


data.plot(y='Carbon Dioxide (ppm)', kind='line',figsize=(12,5), lw=2, title="Carbon Dioxide Levels: Trend")
additiveDecomposition.trend.plot(kind='line',figsize=(12,5), lw=3);

# Finally, here are the seasonal (in blue) and residual components (in red)


additiveDecomposition.seasonal.plot(kind='line',figsize=(12,5), lw=2, title="Seasonality and residual terms")
additiveDecomposition.resid.plot(lw=2);