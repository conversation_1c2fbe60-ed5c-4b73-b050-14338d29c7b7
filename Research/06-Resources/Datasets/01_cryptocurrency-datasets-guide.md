---
title: "Cryptocurrency Datasets for Time Series Forecasting"
permalink: "resources/datasets/cryptocurrency-datasets-guide"
type: "resource"
created: "2025-05-24"
last_updated: "2025-05-24"
tags:
  - datasets
  - cryptocurrency
  - bitcoin
  - ethereum
  - data-sources
models:
  - "All forecasting models"
libraries:
  - "pandas"
  - "yfinance"
  - "ccxt"
  - "requests"
techniques:
  - "data-collection"
  - "data-preprocessing"
  - "feature-engineering"
complexity: "beginner"
summary: "Comprehensive guide to cryptocurrency datasets suitable for time series forecasting, including data sources, preprocessing steps, and feature engineering techniques."
related:
  - "techniques/feature-engineering"
  - "foundations/concepts/time-series-basics"
  - "case-studies/benchmarks/nixtla-model-comparison"
---

# Cryptocurrency Datasets for Time Series Forecasting

## Overview

This guide provides comprehensive information about cryptocurrency datasets suitable for time series forecasting research and applications. It covers data sources, preprocessing techniques, and feature engineering approaches.

## Primary Data Sources

### 1. Yahoo Finance (yfinance)

**Best for**: Historical daily data, easy access
**Coverage**: Major cryptocurrencies (BTC, ETH, etc.)
**Frequency**: Daily, some hourly

```python
import yfinance as yf
import pandas as pd

def get_crypto_data_yahoo(symbol, period="2y", interval="1d"):
    """Fetch cryptocurrency data from Yahoo Finance"""
    ticker = yf.Ticker(f"{symbol}-USD")
    data = ticker.history(period=period, interval=interval)
    
    # Clean column names
    data.columns = [col.lower() for col in data.columns]
    data = data.reset_index()
    
    return data

# Example usage
btc_data = get_crypto_data_yahoo("BTC", period="5y", interval="1d")
eth_data = get_crypto_data_yahoo("ETH", period="3y", interval="1h")
```

### 2. CoinGecko API

**Best for**: Comprehensive market data, free tier available
**Coverage**: 10,000+ cryptocurrencies
**Frequency**: Various (1m to 1d)

```python
import requests
import pandas as pd
from datetime import datetime, timedelta

def get_coingecko_data(coin_id, vs_currency="usd", days=365):
    """Fetch data from CoinGecko API"""
    url = f"https://api.coingecko.com/api/v3/coins/{coin_id}/market_chart"
    params = {
        'vs_currency': vs_currency,
        'days': days,
        'interval': 'hourly' if days <= 90 else 'daily'
    }
    
    response = requests.get(url, params=params)
    data = response.json()
    
    # Convert to DataFrame
    df = pd.DataFrame({
        'timestamp': [item[0] for item in data['prices']],
        'price': [item[1] for item in data['prices']],
        'market_cap': [item[1] for item in data['market_caps']],
        'volume': [item[1] for item in data['total_volumes']]
    })
    
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
    return df.set_index('datetime')

# Example usage
btc_data = get_coingecko_data('bitcoin', days=730)
eth_data = get_coingecko_data('ethereum', days=365)
```

### 3. Binance API

**Best for**: High-frequency data, professional trading
**Coverage**: 500+ trading pairs
**Frequency**: 1m to 1M

```python
import ccxt
import pandas as pd

def get_binance_data(symbol, timeframe='1h', limit=1000):
    """Fetch data from Binance using ccxt"""
    exchange = ccxt.binance()
    
    # Fetch OHLCV data
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
    
    # Convert to DataFrame
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
    
    return df.set_index('datetime')

# Example usage
btc_data = get_binance_data('BTC/USDT', timeframe='1h', limit=2000)
eth_data = get_binance_data('ETH/USDT', timeframe='15m', limit=1000)
```

### 4. Specialized Crypto Data Providers

#### CryptoCompare
```python
def get_cryptocompare_data(fsym, tsym, limit=2000, aggregate=1):
    """Fetch data from CryptoCompare"""
    url = "https://min-api.cryptocompare.com/data/v2/histohour"
    params = {
        'fsym': fsym,
        'tsym': tsym,
        'limit': limit,
        'aggregate': aggregate
    }
    
    response = requests.get(url, params=params)
    data = response.json()['Data']['Data']
    
    df = pd.DataFrame(data)
    df['datetime'] = pd.to_datetime(df['time'], unit='s')
    
    return df.set_index('datetime')
```

## Dataset Characteristics

### Bitcoin (BTC) Datasets

#### Daily Data (2010-Present)
- **Size**: ~5,000 observations
- **Features**: OHLCV, market cap
- **Characteristics**: Long-term trends, lower volatility
- **Best for**: Long-term forecasting, trend analysis

#### Hourly Data (2017-Present)
- **Size**: ~60,000 observations
- **Features**: OHLCV, technical indicators
- **Characteristics**: High volatility, intraday patterns
- **Best for**: Short-term forecasting, trading strategies

#### Minute Data (Recent 2-3 years)
- **Size**: 1M+ observations
- **Features**: OHLCV, order book data
- **Characteristics**: Very high frequency, noise
- **Best for**: High-frequency trading, microstructure analysis

### Ethereum (ETH) Datasets

#### Key Differences from Bitcoin
- **DeFi Integration**: TVL, DEX volumes
- **Gas Fees**: Network congestion indicator
- **Staking Data**: Post-merge staking metrics
- **Developer Activity**: GitHub commits, active addresses

### Alternative Cryptocurrencies

#### High Market Cap (Top 10)
- More stable, better data quality
- Longer historical data available
- Good for comparative studies

#### Mid-Cap Cryptocurrencies
- Higher volatility
- Less historical data
- Interesting for volatility modeling

## Data Preprocessing Pipeline

### 1. Data Cleaning

```python
def clean_crypto_data(df):
    """Standard cleaning pipeline for crypto data"""
    # Remove duplicates
    df = df.drop_duplicates()
    
    # Handle missing values
    df = df.fillna(method='ffill').fillna(method='bfill')
    
    # Remove outliers (price spikes > 50% in 1 hour)
    df['price_change'] = df['close'].pct_change()
    outlier_mask = abs(df['price_change']) > 0.5
    df.loc[outlier_mask, 'close'] = df['close'].rolling(3, center=True).median()
    
    # Ensure positive prices
    df = df[df['close'] > 0]
    
    return df
```

### 2. Feature Engineering

```python
def engineer_crypto_features(df):
    """Create technical and fundamental features"""
    
    # Price-based features
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    df['price_ma_7'] = df['close'].rolling(7).mean()
    df['price_ma_30'] = df['close'].rolling(30).mean()
    
    # Volatility features
    df['volatility_24h'] = df['returns'].rolling(24).std()
    df['volatility_7d'] = df['returns'].rolling(168).std()
    
    # Volume features
    df['volume_ma_24h'] = df['volume'].rolling(24).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma_24h']
    
    # Technical indicators
    df['rsi'] = calculate_rsi(df['close'])
    df['macd'], df['macd_signal'] = calculate_macd(df['close'])
    df['bb_upper'], df['bb_lower'] = calculate_bollinger_bands(df['close'])
    
    # Time-based features
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['month'] = df.index.month
    
    return df

def calculate_rsi(prices, window=14):
    """Calculate Relative Strength Index"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))
```

### 3. Data Validation

```python
def validate_crypto_dataset(df):
    """Validate dataset quality"""
    validation_results = {}
    
    # Check for missing values
    validation_results['missing_values'] = df.isnull().sum().sum()
    
    # Check for price anomalies
    price_changes = df['close'].pct_change()
    validation_results['extreme_changes'] = (abs(price_changes) > 0.5).sum()
    
    # Check temporal consistency
    time_diffs = df.index.to_series().diff()
    expected_freq = time_diffs.mode()[0]
    validation_results['irregular_intervals'] = (time_diffs != expected_freq).sum()
    
    # Check volume consistency
    validation_results['zero_volume'] = (df['volume'] == 0).sum()
    
    return validation_results
```

## Recommended Datasets by Use Case

### 1. Academic Research

**Bitcoin Daily (2015-2024)**
- Source: Yahoo Finance or CoinGecko
- Features: OHLCV + market cap
- Size: ~3,000 observations
- Good for: Long-term trend analysis

### 2. Trading Strategy Development

**Bitcoin/Ethereum Hourly (2020-2024)**
- Source: Binance API
- Features: OHLCV + technical indicators
- Size: ~35,000 observations
- Good for: Intraday strategy testing

### 3. High-Frequency Analysis

**Bitcoin Minute Data (Recent 6 months)**
- Source: Binance or professional providers
- Features: OHLCV + order book
- Size: 250,000+ observations
- Good for: Microstructure analysis

### 4. Multi-Asset Studies

**Top 10 Cryptocurrencies (2020-2024)**
- Source: CoinGecko API
- Features: OHLCV + market metrics
- Good for: Portfolio optimization

## Data Quality Considerations

### Common Issues

1. **Exchange Downtime**: Missing data during outages
2. **Flash Crashes**: Extreme price movements
3. **Low Liquidity**: Unreliable prices for small coins
4. **Fork Events**: Price discontinuities
5. **Timezone Issues**: Inconsistent timestamps

### Quality Metrics

```python
def calculate_data_quality_score(df):
    """Calculate overall data quality score"""
    scores = {}
    
    # Completeness (0-100)
    scores['completeness'] = (1 - df.isnull().sum().sum() / df.size) * 100
    
    # Consistency (0-100)
    price_changes = df['close'].pct_change()
    extreme_changes = (abs(price_changes) > 0.5).sum()
    scores['consistency'] = max(0, 100 - extreme_changes / len(df) * 1000)
    
    # Timeliness (0-100)
    time_gaps = df.index.to_series().diff()
    expected_freq = time_gaps.mode()[0]
    irregular_count = (time_gaps != expected_freq).sum()
    scores['timeliness'] = max(0, 100 - irregular_count / len(df) * 100)
    
    # Overall score
    overall_score = sum(scores.values()) / len(scores)
    
    return scores, overall_score
```

## Best Practices

### 1. Data Collection
- Use multiple sources for validation
- Implement rate limiting for APIs
- Store raw data before preprocessing
- Document data lineage

### 2. Preprocessing
- Handle missing data appropriately
- Validate outlier removal
- Maintain temporal order
- Document all transformations

### 3. Feature Engineering
- Create domain-specific features
- Avoid look-ahead bias
- Validate feature importance
- Consider computational cost

### 4. Storage and Management
- Use efficient formats (Parquet, HDF5)
- Implement version control
- Regular data updates
- Backup strategies

## Conclusion

Quality cryptocurrency datasets are essential for reliable forecasting models. Key considerations include:

1. **Source Selection**: Choose based on frequency and coverage needs
2. **Data Quality**: Implement robust validation and cleaning
3. **Feature Engineering**: Create relevant domain-specific features
4. **Maintenance**: Regular updates and quality monitoring

This guide provides the foundation for building robust cryptocurrency forecasting datasets suitable for research and production applications.
