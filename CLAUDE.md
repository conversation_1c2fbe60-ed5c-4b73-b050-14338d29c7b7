# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Status & Overview

**Current Phase**: Research Completed, Implementation Pending (May 2025)

Advanced time series forecasting research repository focused on cryptocurrency price prediction using Nixtla's forecasting ecosystem (StatsForecast, NeuralForecast, MLForecast).

### Project Objectives
- Implement production-ready Bitcoin intraday forecasting system
- Leverage Nixtla's ecosystem for optimal ensemble performance
- Achieve <0.65 RMSE for 1-hour ahead Bitcoin predictions
- Build scalable, GPU-optimized pipeline for real-time forecasting

### Core Technologies
- **NeuralForecast**: PatchTST (primary), LSTM, GRU models
- **MLForecast**: LightGBM, XGBoost for regime detection
- **StatsForecast**: ARIMA, ETS baselines, GARCH for volatility
- **Infrastructure**: GPU (8GB+ VRAM), Python 3.8+, Docker

## Repository Structure

```
Nixtla-Forecasting-Ensemble/
├── CLAUDE.md                           # This file - project guidance
├── Research/                           # Research documents and examples
│   ├── CLAUDE.md                      # Research navigation hub
│   ├── Applications/                  # Domain-specific implementations
│   │   └── Bitcoin/                   # Bitcoin forecasting guides
│   ├── Architecture/                  # System design patterns
│   ├── Forecasting/                   # Core methodologies
│   │   ├── Applications/             # Applied forecasting
│   │   ├── Models/                   # Model examples
│   │   └── Techniques/               # Forecasting techniques
│   ├── Models/                        # ML/Statistical models
│   │   ├── DecisionForests/          # Tree-based models
│   │   └── Neural/                   # Neural network examples
│   └── Synthesis/                     # Comprehensive analyses
├── Plans:Templates:Tools:Updates/      # Planning and utilities
│   ├── Plans/                         # Implementation plans
│   ├── Templates/                     # Document templates
│   ├── Tools/                         # Utility scripts
│   └── Updates/                       # Project updates
├── modules/                           # Global configuration
└── Tools/                             # Production tools (future)
```

### File Naming Convention
- Format: `primary-topic_specific-descriptor.extension`
- Examples: 
  - `bitcoin_forecasting-guide.md`
  - `lstm_sine-wave-example.py`
  - `ensemble_dynamic-weighting-strategies.md`

## Implementation Gaps & Roadmap

### Critical Gaps (High Priority)
1. **No Production PatchTST Implementation**: Need Nixtla-integrated PatchTST pipeline
2. **Missing Real-time Data Pipeline**: No Bitcoin price feed integration
3. **Incomplete Feature Engineering**: Technical indicators not implemented
4. **No Model Registry**: Version control and experiment tracking missing

### Medium Priority Gaps
5. **Hyperparameter Optimization**: No automated tuning pipeline
6. **Deployment Infrastructure**: Docker/Kubernetes configs needed
7. **Monitoring System**: Model performance tracking missing
8. **API Layer**: REST/gRPC endpoints for predictions

### Implementation Roadmap

#### Phase 1: Foundation (Weeks 1-2)
- [ ] Set up development environment with GPU support
- [ ] Implement data ingestion pipeline (yfinance → DataFrame)
- [ ] Create feature engineering module (technical indicators)
- [ ] Build data preprocessing pipeline (scaling, transformations)

#### Phase 2: Model Implementation (Weeks 3-4)
- [ ] Implement PatchTST with NeuralForecast
- [ ] Add LSTM/GRU models as alternatives
- [ ] Integrate MLForecast models (LightGBM, XGBoost)
- [ ] Add StatsForecast baselines (ARIMA, GARCH)

#### Phase 3: Ensemble & Optimization (Weeks 5-6)
- [ ] Build ensemble integration layer
- [ ] Implement dynamic weighting based on market regimes
- [ ] Add hyperparameter optimization (Optuna)
- [ ] Create model registry with MLflow

#### Phase 4: Production (Weeks 7-8)
- [ ] Dockerize application
- [ ] Add REST API with FastAPI
- [ ] Implement monitoring with Prometheus/Grafana
- [ ] Deploy to cloud (AWS/GCP)

## Common Development Tasks

### Running Python Scripts
```bash
# Run forecasting examples
python Research/Forecasting/Models/lstm_sine-wave-example.py
python Research/Forecasting/Techniques/decomposition_time-series-example.py
python Research/Models/Neural/simple_neural-network-regression.py

# Convert notebooks if needed
python Plans:Templates:Tools:Updates/Tools/convert_notebooks.py
```

### Setup Development Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install core dependencies
pip install numpy pandas matplotlib scikit-learn
pip install yfinance ta  # For data and technical indicators

# Install Nixtla ecosystem
pip install statsforecast neuralforecast mlforecast

# GPU support for neural models
pip install torch  # or tensorflow-gpu depending on preference

# Development tools
pip install jupyter black pytest optuna mlflow
```

### Quick Start Examples

#### 1. Load Bitcoin Data
```python
from datasetsforecast import load_dataset
import yfinance as yf
import pandas as pd

# Option 1: yfinance for real-time data
btc = yf.download('BTC-USD', start='2023-01-01', interval='1h')
df = pd.DataFrame({
    'unique_id': 'BTC',
    'ds': btc.index,
    'y': btc['Close']
})

# Option 2: Use Nixtla's sample crypto data
# df = load_dataset('bitcoin', freq='H')
```

#### 2. Quick PatchTST Implementation
```python
from neuralforecast import NeuralForecast
from neuralforecast.models import PatchTST

# Configure PatchTST for Bitcoin
models = [PatchTST(
    h=24,                    # 24-hour forecast
    input_size=168,          # 7 days of hourly data
    patch_len=32,            # Optimal for hourly
    stride=8,
    revin=True,              # Handle non-stationarity
    scaler_type='robust',    # For outliers
    max_steps=1000,
    early_stop_patience=50,
    batch_size=32
)]

# Train and predict
nf = NeuralForecast(models=models, freq='H')
nf.fit(df=df)
forecasts = nf.predict()
```

### Search Codebase
```bash
# Find model implementations
find Research/ -name "*.py" -type f | xargs grep -l "LSTM\|model\|forecast"

# Search research documents
grep -r "Bitcoin" Research/ --include="*.md"
grep -r "PatchTST\|ensemble" Research/ --include="*.md"
```

## High-Level Architecture

### Modular Hybrid Forecasting System
The codebase implements a layered architecture pattern (`Research/Architecture/modular_hybrid-forecasting-architecture.md`) with:

1. **Data Layer**: Flexible ingestion supporting multiple sources and formats
2. **Model Layer**: Factory pattern for model instantiation (ARIMA, LSTM, XGBoost, etc.)
3. **Integration Layer**: Hybrid ensemble module for combining predictions
4. **Deployment Layer**: Standardized interfaces for production use

### Pipeline Pattern Implementation
```
Raw Data → Preprocessing → Feature Engineering → Model Training → Ensemble → Prediction
```

Key pipeline characteristics:
- MSTL decomposition with periods [24, 168] for crypto data
- Window-based MinMaxScaling for neural models
- Dynamic ensemble weighting based on market regimes
- GPU optimization for neural models (8GB+ VRAM required)

### Cross-Component Integration
The system uses dependency injection and factory patterns to enable:
- Dynamic model selection based on data characteristics
- Flexible weighting schemes for ensemble predictions
- Real-time model performance monitoring
- Graceful fallback to simpler models on failure

### Key Design Decisions

1. **120-day hourly data window** (~2880 points) for manageable context and training
2. **PatchTST as primary neural model** (21% lower error than traditional models)
3. **Log transformations + robust scaling** for handling Bitcoin's volatility
4. **Technical indicators + on-chain metrics** for feature engineering (5-12% accuracy boost)
5. **Huber loss for LSTM** during volatile periods
6. **Dynamic ensemble weighting** (13-17% improvement over static)

### Model Performance Benchmarks
- PatchTST: Best for intraday forecasting (patch_len=32 for hourly)
- Bi-LSTM: 97.50% accuracy on hourly Bitcoin with proper features
- LightGBM: Superior for market regime detection
- GARCH: Essential for volatility modeling
- Ensemble: Dynamic weighting outperforms individual models

## Research Organization

Research follows hierarchical structure optimized for AI retrieval:
- `/Research/Synthesis/`: High-level synthesis documents combining approaches
- `/Research/Applications/Bitcoin/`: Bitcoin-specific implementations
- `/Research/Forecasting/Models/`: Model implementations with examples
- `/Research/Architecture/`: System design patterns and interfaces

Key documents:
- `Research/Synthesis/bitcoin_forecasting-complete-synthesis.md` - Complete synthesis
- `Research/Architecture/modular_hybrid-forecasting-architecture.md` - Architecture patterns
- `Research/Applications/Bitcoin/intraday_forecasting-guide.md` - Implementation guide

See `/Research/CLAUDE.md` for complete navigation.

## Metadata Standards

### Markdown Files (Research Documents)
All markdown files should include YAML front matter:

```yaml
---
title: "Document Title"
permalink: "category/document-name"
type: "technical-report|example|guide|synthesis"
created: "YYYY-MM-DD"
last_updated: "YYYY-MM-DD"
tags: 
  - bitcoin
  - forecasting
  - ensemble
models:
  - PatchTST
  - LSTM
techniques:
  - ensemble-weighting
  - feature-engineering
summary: "Brief description of content"
related:
  - "path/to/related-doc1"
  - "path/to/related-doc2"
---
```

### Python Files (Code Examples)
Include structured docstrings:

```python
"""
# Bitcoin Forecasting with PatchTST

## Metadata
title: Bitcoin PatchTST Implementation
type: example
tags: bitcoin, patchtst, neural-forecast
models: PatchTST
techniques: time-series-decomposition, revin-normalization
summary: Production-ready PatchTST implementation for Bitcoin
related:
  - Research/Synthesis/bitcoin_forecasting-complete-synthesis.md
  - Research/Applications/Bitcoin/intraday_forecasting-guide.md
"""
```

## Planning Documents

### Using Planning Templates
Navigate to `Plans:Templates:Tools:Updates/Templates/` for:
- `ImplementationGuideTemplate.md` - For new feature implementation
- `ResearchReportTemplate.md` - For research documentation
- `PythonScriptTemplate.py` - For new code files

### Active Plans
Check `Plans:Templates:Tools:Updates/Plans/` for:
- `FeatureImplementationPlan.md` - Overall implementation strategy
- `Research-Organization-Plan.md` - Research structure guidelines
- `MCP-Streamlining-Plan.md` - Code optimization approaches

## Testing & Validation

### Model Validation Framework
```python
from neuralforecast.losses import mae, mse, rmse
from neuralforecast.utils import plot

# Backtesting with time series cross-validation
from neuralforecast.core import NeuralForecast
nf = NeuralForecast(models=models, freq='H')

# Use cross-validation for robust evaluation
cv_results = nf.cross_validation(
    df=df,
    n_windows=5,           # 5 validation windows
    step_size=24,          # 24-hour steps
    h=24                   # 24-hour horizon
)

# Calculate metrics
from neuralforecast.losses import mae, rmse
metrics = cv_results.groupby(['unique_id', 'cutoff']).apply(
    lambda x: pd.Series({
        'mae': mae(x['y'], x['PatchTST']),
        'rmse': rmse(x['y'], x['PatchTST'])
    })
)
```

### Performance Benchmarks
Target metrics based on research:
- **1-hour forecast**: RMSE < 0.65, MAE < 0.56
- **24-hour forecast**: RMSE < 1.2, MAE < 0.95
- **Directional accuracy**: > 65%
- **Training time**: < 60 min on GPU (hourly data)

## Advanced Nixtla Patterns

### Dynamic Ensemble with Market Regime Detection
```python
from mlforecast import MLForecast
from mlforecast.lag_transforms import RollingMean, ExponentiallyWeightedMean
import lightgbm as lgb

# Regime detection model
regime_model = MLForecast(
    models=[lgb.LGBMClassifier()],
    freq='H',
    lags=[1, 24, 168],
    lag_transforms={
        1: [ExponentiallyWeightedMean(alpha=0.95)],
        24: [RollingMean(window_size=24)]
    }
)

# Train regime classifier
regime_model.fit(df_with_regimes)

# Adjust ensemble weights based on regime
def get_ensemble_weights(regime):
    weights = {
        'volatile_bull': {'PatchTST': 0.4, 'LSTM': 0.3, 'LightGBM': 0.3},
        'stable': {'ARIMA': 0.5, 'PatchTST': 0.3, 'LightGBM': 0.2},
        'volatile_bear': {'GARCH': 0.4, 'PatchTST': 0.3, 'XGBoost': 0.3}
    }
    return weights.get(regime, {'PatchTST': 0.5, 'LSTM': 0.5})
```

### Feature Engineering Pipeline
```python
import ta  # Technical Analysis library

def engineer_features(df):
    # Price features
    df['returns'] = df['y'].pct_change()
    df['log_returns'] = np.log(df['y'] / df['y'].shift(1))
    
    # Technical indicators
    df['rsi'] = ta.momentum.RSIIndicator(df['y'], window=14).rsi()
    df['macd'] = ta.trend.MACD(df['y']).macd_diff()
    df['bb_distance'] = (df['y'] - ta.volatility.BollingerBands(df['y']).bollinger_mavg()) / ta.volatility.BollingerBands(df['y']).bollinger_wband()
    
    # Volatility features
    df['realized_vol'] = df['returns'].rolling(24).std()
    df['parkinson_vol'] = np.sqrt(np.log(df['high']/df['low'])**2 / (4*np.log(2)))
    
    return df
```

## Production Deployment Checklist

- [ ] GPU environment configured (CUDA 11.8+)
- [ ] Data pipeline with fallback sources
- [ ] Model versioning with MLflow
- [ ] API endpoints documented
- [ ] Monitoring dashboards configured
- [ ] Automated retraining schedule
- [ ] Performance alerts set up
- [ ] Backup prediction models ready

## Contributing & Development Guidelines

### Code Style
- Python: Follow PEP 8, use Black formatter
- Documentation: Include docstrings for all functions
- Git: Conventional commits (feat:, fix:, docs:, etc.)

### Testing Requirements
- Unit tests for all new functions
- Integration tests for model pipelines
- Performance benchmarks for new models
- Cross-validation results in PRs

### Adding New Models
1. Implement in appropriate Nixtla framework
2. Add to model factory pattern
3. Include hyperparameter search space
4. Document performance benchmarks
5. Update ensemble weight recommendations

## Getting Help

### Resources
- **Nixtla Documentation**: https://nixtla.github.io/
- **Research Synthesis**: `/Research/Synthesis/bitcoin_forecasting-complete-synthesis.md`
- **Architecture Guide**: `/Research/Architecture/modular_hybrid-forecasting-architecture.md`
- **Planning Templates**: `/Plans:Templates:Tools:Updates/Templates/`

### Common Issues
1. **GPU Memory**: Reduce batch_size or input_size for PatchTST
2. **Data Quality**: Check for missing values, use forward fill
3. **Convergence**: Increase max_steps, adjust learning rate
4. **Ensemble Weights**: Start with equal weights, optimize gradually

## Summary

This project implements a state-of-the-art Bitcoin forecasting system using Nixtla's ecosystem. The research phase has identified PatchTST as the optimal model for intraday forecasting, with dynamic ensemble weighting providing significant performance improvements. The implementation roadmap provides a clear path from research to production deployment.

Key next steps:
1. Set up development environment with GPU support
2. Implement data pipeline with feature engineering
3. Build model implementations starting with PatchTST
4. Create ensemble integration with dynamic weighting
5. Deploy with monitoring and automated retraining

For questions or contributions, refer to the planning documents and templates provided.