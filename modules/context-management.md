# Context Management Module

*Optimizing Opus 4's context window through intelligent usage patterns*

## 🎯 Core Purpose

<Purpose>
Claude <PERSON> automatically displays context usage percentage and manages compaction.
This module helps you work efficiently within these constraints while maintaining
task continuity through the three-component architecture.
</Purpose>

## 📊 Working with Automatic Context Display

<ContextMonitoring>
  Claude Code shows real-time percentage (e.g., "42% context used")
  
  <UsagePatterns>
    0-40%: Free operations zone
    - Load comprehensive context
    - Explore codebase extensively
    - Build full mental models
    
    40-70%: Efficiency zone
    - Focus on essential operations
    - Start organizing for checkpoints
    - Consider task boundaries
    
    70-85%: Checkpoint zone
    - Complete current subtasks
    - Sync with Planning and Memory
    - Prepare for natural break
    
    85%+: Transition zone
    - Finish immediate work only
    - Create comprehensive handoff
    - Start fresh with context preserved
  </UsagePatterns>
</ContextMonitoring>

## 🔗 Integration Points

<SystemIntegration>
  <WithPlanning>
    At natural task boundaries:
    - <PERSON> to<PERSON> often align with good break points
    - Update Planning MCP status before transitions
    - Use todo completion as checkpoint trigger
  </WithPlanning>
  
  <WithMemory>
    Before context resets:
    - Document current implementation state
    - Save architectural decisions
    - Note any unresolved questions
    - Reference active Planning tasks
  </WithMemory>
  
  <AutomaticSync>
    <PERSON> handles compaction automatically
    - Preserves essential context in summary
    - Maintains task continuity
    - No manual intervention needed
  </AutomaticSync>
</SystemIntegration>

## 🎭 Context Usage Strategies

<Strategies>
  <ForSimpleTasks>
    Single-session work:
    - Use context freely
    - No special management needed
    - Let automatic systems handle optimization
  </ForSimpleTasks>
  
  <ForComplexProjects>
    Multi-session work:
    - Plan around Claude todos as checkpoints
    - Sync three components at each todo
    - Use high context periods for exploration
    - Use low context for implementation
  </ForComplexProjects>
  
  <ForDebugging>
    Investigation work:
    - Front-load context gathering
    - Document findings immediately in Memory
    - Create Planning tasks for fixes
    - Complete before context fills
  </ForDebugging>
</Strategies>

## 🚀 Practical Patterns

<Patterns>
  <EarlySession>
    With fresh context (0-40%):
    - Load comprehensive project state
    - Read relevant documentation
    - Build full understanding
    - Plan implementation approach
  </EarlySession>
  
  <MidSession>
    As context grows (40-70%):
    - Focus on implementation
    - Update systems at milestones
    - Prepare for eventual transition
  </MidSession>
  
  <LateSession>
    Approaching limits (70%+):
    - Complete current Claude todo
    - Ensure Planning MCP updated
    - Document in Memory
    - Natural transition point
  </LateSession>
</Patterns>

## ✅ Best Practices

<BestPractices>
  DO:
  ✓ Trust automatic context management
  ✓ Use Claude todos as natural checkpoints
  ✓ Sync three components at boundaries
  ✓ Work naturally without context anxiety
  ✓ Let percentage guide, not restrict
  
  DON'T:
  ✗ Manually track tokens
  ✗ Artificially limit exploration
  ✗ Rush to avoid context growth
  ✗ Ignore the percentage indicator
  ✗ Fight the automatic systems
</BestPractices>

## 📋 Quick Reference

<QuickReference>
  Context awareness in practice:
  
  - See 25%? → Explore freely
  - See 60%? → Focus on current todo
  - See 80%? → Prepare checkpoint
  - See 90%? → Wrap up gracefully
  
  Remember: The system handles optimization.
  Your job is to work effectively within it.
</QuickReference>

---
*Work naturally with Claude Code's automatic features. Context management enables continuous productivity, not restriction.*