# Workflow Planning & Task Synchronization

*Coordinating technical planning with automatic validation through three integrated layers*

## 🏗️ Three-Layer Architecture

<TaskArchitecture>
  This system coordinates three distinct but integrated layers:
  
  <Layer name="Software Planning MCP" role="Technical Foundation">
    - Detailed technical todos with complexity scores (1-10)
    - Dependencies and relationships between tasks
    - Implementation notes and code examples
    - Granular progress tracking
  </Layer>
  
  <Layer name="Claude Code Todos" role="Validation Checkpoints">
    - Automatically generated high-level milestones
    - User-visible progress indicators
    - Natural sync points for the system
    - Validation that technical work achieves goals
  </Layer>
  
  <Layer name="Basic Memory MCP" role="Knowledge Persistence">
    - Decisions and rationale documentation
    - Implementation patterns discovered
    - Cross-session context preservation
    - Architectural knowledge accumulation
  </Layer>
</TaskArchitecture>

## 🔄 Synchronization Protocol

<SyncProtocol>
  <WhenTodoAppears>
    When Claude Code generates: "✓ Implement user authentication"
    
    1. **Identify Scope**: Recognize this as a validation checkpoint
    2. **Check Planning**: Query Software Planning MCP for related tasks:
       - JWT token implementation (complexity: 6)
       - Middleware setup (complexity: 5)
       - Login endpoint (complexity: 4)
       - Password hashing (complexity: 3)
    3. **Begin Work**: Start with highest priority technical task
  </WhenTodoAppears>
  
  <DuringImplementation>
    As you work through technical tasks:
    
    1. **Update Planning MCP**: Mark technical todos as in-progress
    2. **Document Decisions**: Key choices go to Memory MCP immediately
    3. **Track Progress**: Both in Planning MCP and visually in Claude todos
  </DuringImplementation>
  
  <OnTodoCompletion>
    When Claude marks todo complete: "✓ ~~Implement user authentication~~"
    
    1. **Verify Technical Tasks**: Ensure all related Planning MCP tasks done
    2. **Document Implementation**: Store approach in Memory MCP
    3. **Update Status**: Mark all technical tasks complete
    4. **Note Learnings**: Any patterns or insights to Memory
  </OnTodoCompletion>
</SyncProtocol>

## 💡 Practical Examples

<Example name="API Endpoint Implementation">
  Scenario: Building a REST API for a blog platform
  
  Claude Todo Generated: "✓ Create blog post CRUD endpoints"
  
  Planning MCP Breakdown:
  - Design RESTful routes (complexity: 3)
  - Implement POST /posts endpoint (complexity: 5)
  - Implement GET /posts/:id endpoint (complexity: 4)
  - Implement PUT /posts/:id endpoint (complexity: 5)
  - Implement DELETE /posts/:id endpoint (complexity: 4)
  - Add validation middleware (complexity: 6)
  - Write integration tests (complexity: 7)
  
  Synchronization Flow:
  1. Work through each endpoint implementation
  2. As each completes, update Planning MCP
  3. When all done, Claude todo auto-completes
  4. Document API patterns in Memory MCP
  5. Note any reusable middleware created
</Example>

<Example name="Database Migration">
  Claude Todo Generated: "✓ Set up database schema"
  
  Planning MCP Breakdown:
  - Design entity relationships (complexity: 7)
  - Create migration files (complexity: 4)
  - Implement User model (complexity: 5)
  - Implement Post model (complexity: 5)
  - Set up associations (complexity: 6)
  - Add indexes (complexity: 5)
  
  Natural Sync Points:
  - After relationship design → document in Memory
  - After each model → update Planning status
  - On todo completion → store migration patterns
</Example>

## 🎯 Natural Checkpoints

<Checkpoints>
  <Major>
    Claude todo completion (primary sync point)
    - Triggers full synchronization
    - Updates all three systems
    - Documents implementation approach
  </Major>
  
  <Minor>
    During implementation:
    - Architecture decisions → immediate Memory sync
    - Complex problem solved → document pattern
    - Reusable component created → store for future
    - Integration point defined → update Planning
  </Minor>
  
  <Proactive>
    Before context resets:
    - Ensure Planning MCP has current status
    - Critical decisions are in Memory
    - Claude todos reflect actual progress
  </Proactive>
</Checkpoints>

## 🛠️ Working Patterns

<Patterns>
  <SimpleTask>
    For straightforward implementations:
    1. Claude generates single todo
    2. 2-3 technical tasks in Planning
    3. Complete linearly
    4. Single memory entry on completion
  </SimpleTask>
  
  <ComplexFeature>
    For multi-component features:
    1. Claude may generate multiple related todos
    2. Planning MCP has detailed dependency graph
    3. Work on parallel tasks when possible
    4. Sync at each todo completion
    5. Comprehensive memory documentation
  </ComplexFeature>
  
  <Debugging>
    When fixing issues:
    1. Claude todo: "✓ Fix authentication bug"
    2. Planning: Investigation tasks + fix tasks
    3. Document root cause in Memory
    4. Update Planning with solution
  </Debugging>
</Patterns>

## 🔗 Integration Commands

<Commands>
  Create these in .claude/commands/ for consistency:
  
  sync-planning.md:
  "Check alignment between Claude todos and Planning MCP tasks"
  
  complete-milestone.md:
  "Update Planning MCP when Claude todo completes"
  
  document-decision.md:
  "Store architectural decision in Memory with context"
</Commands>

## 📋 Quick Reference

<QuickReference>
  Three-layer sync in 30 seconds:
  
  1. Claude todo appears → Check Planning MCP
  2. Work on technical tasks → Update as you go
  3. Todo completes → Sync all systems
  4. Document learnings → Build knowledge base
  
  Remember: The layers complement, not compete.
  - Planning = How to build
  - Claude Todos = What to build  
  - Memory = Why we built it that way
</QuickReference>

---
*This system turns automatic todos into a powerful validation layer while maintaining detailed technical tracking and persistent knowledge.*