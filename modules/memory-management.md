# Memory Management Module

*Effective knowledge persistence with context awareness*

## 🎯 Quick Reference

<MemoryTools>
  Primary: Basic Memory MCP
  - `write_note`: Persist important knowledge
  - `search_notes`: Find previous work and decisions
  - `build_context`: Construct comprehensive knowledge graphs
  - `recent_activity`: Track project changes
  - `sync`: Ensure file system consistency
  
  Visible: TodoWrite
  - User-facing task tracking
  - Real-time progress visibility
  
  Fallback: Direct file operations
  - When MCPs unavailable
  - Markdown files in project structure
</MemoryTools>

## 🔄 Session Lifecycle

<SessionProtocol>
  <Start mode="adaptive">
    Quick start for simple tasks:
    1. Check active todos (TodoRead)
    2. Load recent context if relevant
    3. Begin work immediately
    
    Comprehensive start for complex tasks:
    1. Full context build from project memory
    2. Review all related previous work
    3. Understand complete problem space
    4. Plan with full historical context
  </Start>
  
  <During mode="natural">
    Document based on task needs:
    - Critical decisions: Immediate detailed notes
    - Explorations: Comprehensive documentation
    - Simple changes: Brief observations
    - Complex debugging: Full trace history
    
    Work with automatic optimization:
    - /compact preserves context automatically
    - Memory persists across compacts
    - Document freely without restrictions
    - Summaries complement your notes
  </During>
  
  <End mode="thorough">
    Create useful handoff for future sessions:
    1. Detailed accomplishments
    2. All key decisions with rationale
    3. Complete blocker analysis
    4. Comprehensive next steps
    5. Links to all relevant artifacts
    6. Save with descriptive naming
  </End>
</SessionProtocol>

## 🔍 Search Strategies

<SearchPatterns>
  <QuickFind>
    For recent or specific items:
    - Use focused queries with project/component terms
    - Check recent_activity for today's work
    - Search by specific tags or identifiers
  </QuickFind>
  
  <DeepSearch>
    For complex investigations:
    - Build comprehensive context graphs
    - Search across all related projects
    - Follow relation chains thoroughly
    - No depth limits when needed
  </DeepSearch>
  
  <HistoricalAnalysis>
    For understanding evolution:
    - Trace decision history
    - Review all related sessions
    - Analyze patterns over time
    - Document findings comprehensively
  </HistoricalAnalysis>
</SearchPatterns>

## 🔗 Planning System Integration

<PlanningIntegration>
  Memory and Software Planning MCP work together at key synchronization points:
  
  <SyncPoints>
    When completing major milestones:
    - Claude todo shows completion
    - Update Planning MCP task status
    - Document implementation approach in Memory
    - Cross-reference between systems
    
    After architectural decisions:
    - Immediate Memory documentation
    - Reference related Planning tasks
    - Note rationale and alternatives
    - Tag for future retrieval
    
    Before context resets:
    - Ensure Planning status current
    - Critical decisions in Memory
    - Active task state preserved
  </SyncPoints>
  
  <Example>
    When Claude todo "✓ API implementation" completes:
    
    1. **Check Planning MCP**: Which technical tasks comprised this?
       - JWT middleware (complete)
       - Rate limiting (complete)
       - Error handling (complete)
    
    2. **Document in Memory**:
       ```
       Title: API Implementation Approach
       - Used Express middleware pattern
       - JWT with refresh token rotation
       - Rate limiting: 100 req/min per user
       Relations: Implements Planning tasks #23-25
       ```
    
    3. **Update Planning**: Mark milestone achieved
    4. **Note patterns**: Reusable middleware created
  </Example>
</PlanningIntegration>

## 🔗 Integration Patterns

<IntegrationStrategy>
  <ThreeSystemSync>
    Full integration with Planning and Claude todos:
    
    1. **Claude Todo appears** → Planning has technical breakdown
    2. **Work progresses** → Update Planning status
    3. **Key decisions** → Document in Memory immediately
    4. **Todo completes** → Full sync across all systems
    
    Natural flow, not forced synchronization.
  </ThreeSystemSync>
  
  <Standard>
    For most development tasks:
    1. Claude todos for visibility
    2. Planning MCP for technical tracking
    3. Memory for knowledge persistence
    4. Sync at todo completion
  </Standard>
  
  <Comprehensive>
    For complex multi-component work:
    1. Multiple Claude todos may appear
    2. Planning tracks detailed dependencies
    3. Memory captures decisions continuously
    4. Sync at each todo checkpoint
  </Comprehensive>
  
  <Fallback>
    When Planning MCP unavailable:
    1. Use Claude todos as primary tracker
    2. Document technical details in Memory
    3. Note what would go to Planning
    4. Sync when Planning returns
  </Fallback>
</IntegrationStrategy>

## 📊 Documentation Approaches

<DocumentationModes>
  <TaskAppropriate>
    Choose based on task needs:
    - Simple tasks: Brief notes
    - Standard work: Clear documentation
    - Complex projects: Comprehensive capture
    - Research: Exhaustive documentation
  </TaskAppropriate>
  
  <WorkingWithCompact>
    Automatic optimization enables:
    - Unlimited documentation depth
    - Natural work patterns
    - Focus on content quality
    - Trust in system optimization
  </WorkingWithCompact>
  
  <MemoryPlusSummaries>
    Dual system advantages:
    - Memory: Permanent knowledge
    - Summaries: Recent context
    - Together: Complete picture
    - Neither has limits
  </MemoryPlusSummaries>
</DocumentationModes>

## 📋 Practical Patterns

<CommonScenarios>
  <ProjectUnderstanding>
    When diving into new/complex area:
    1. Build full context without limits
    2. Read all related documentation
    3. Create comprehensive mental model
    4. Document understanding thoroughly
    5. Create Planning tasks for exploration
  </ProjectUnderstanding>
  
  <DebugComplex>
    For difficult problems:
    1. Document every hypothesis
    2. Track attempted solutions in Planning
    3. Build complete problem history
    4. Create detailed resolution notes
    5. Link Memory findings to Planning fixes
  </DebugComplex>
  
  <DesignDecisions>
    For architectural choices:
    1. Document all options considered
    2. Reference Planning tasks affected
    3. Detailed pros/cons analysis
    4. Full rationale for choice
    5. Update Planning with chosen approach
  </DesignDecisions>
  
  <MilestoneCompletion>
    When Claude todo completes:
    1. List all Planning tasks included
    2. Document implementation approach
    3. Note any deviations from plan
    4. Capture reusable patterns
    5. Update Planning status
  </MilestoneCompletion>
</CommonScenarios>

## 🚦 Memory Best Practices

<BestPractices>
  Effectiveness with automatic optimization:
  - Document comprehensively when needed
  - Trust /compact to handle context
  - Focus on knowledge organization
  - Let summaries maintain continuity
  
  Persistence across sessions:
  - Memory notes survive all compacts
  - Create rich cross-references
  - Build comprehensive knowledge graphs
  - Work naturally across multiple cycles
  
  Future-self friendly:
  - Write detailed notes without worry
  - Include full context and rationale
  - Link related concepts extensively
  - Make knowledge discoverable
  - Summaries + memory = complete picture
</BestPractices>

## 🚀 Enhanced Memory Workflows

<EnhancedWorkflows>
  <ContinuousDocumentation>
    Across multiple /compacts:
    - Document every important insight
    - Build comprehensive decision logs
    - Create rich knowledge networks
    - No artificial stopping points
  </ContinuousDocumentation>
  
  <KnowledgeEvolution>
    Long-term projects:
    - Memory accumulates permanently
    - Summaries track recent progress
    - Together form complete history
    - Reference both for full context
  </KnowledgeEvolution>
  
  <UnlimitedCapture>
    Complex investigations:
    - Write extensive analysis notes
    - Create detailed debug traces
    - Build complete problem histories
    - Let /compact handle optimization
  </UnlimitedCapture>
</EnhancedWorkflows>

## 🔄 Sync Operations

<SyncProtocols>
  <BasicSync>
    Regular synchronization:
    - Run after major changes
    - Ensure file system consistency
    - Update knowledge graph
  </BasicSync>
  
  <ProjectSync>
    When switching contexts:
    - Sync current project state
    - Switch to new project
    - Load relevant context
    - Continue seamlessly
  </ProjectSync>
  
  <WatchMode>
    For active development:
    - Enable continuous sync
    - Real-time updates
    - Background operation
  </WatchMode>
</SyncProtocols>

---
*Memory persists across all context resets. Document freely.*
*Three-system integration creates comprehensive project intelligence.*
*Claude todos + Planning tasks + Memory knowledge = Complete development context.*